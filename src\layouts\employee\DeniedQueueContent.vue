<template>
  <main class="w-full text-white">
    <!-- Header with conditional layout -->
    <div v-if="showHelp" class="py-3 px-6 w-full border-t-0 bg-slate-800 border-slate-300">
      <div class="flex items-center justify-between w-full">
        <!-- Left Section: Employee Dashboard -->
        <div class="text-white font-bold text-lg flex-shrink-0">
          Employee Dashboard
        </div>

        <!-- Center navigation items -->
        <div class="flex items-center gap-4 font-medium text-cyan-400">
          <!-- Next Client -->
          <div class="flex items-center gap-2">
            <img src="/src/assets/help/Next plan.png" class="w-6 h-6" alt="Next Client" />
            <span class="text-cyan-400 text-sm font-medium">Next Client</span>
          </div>

          <div class="w-px h-4 bg-gray-400"></div>

          <!-- Customize -->
          <div class="flex items-center gap-2">
            <img src="/src/assets/help/Dashboard customize.png" class="w-6 h-6" alt="Customize" />
            <span class="text-cyan-400 text-sm font-medium">Customize</span>
          </div>

          <div class="w-px h-4 bg-gray-400"></div>

          <!-- Help -->
          <div class="flex items-center gap-2">
            <img src="/src/assets/help/Help.png" class="w-6 h-6" alt="Help" />
            <button
              @click="navigateToHelp"
              class="hover:text-cyan-300 transition-colors text-cyan-400 text-sm font-medium"
              :class="{ 'text-cyan-300': showHelp }"
            >
              Help
            </button>
          </div>

          <div class="w-px h-4 bg-gray-400"></div>

          <!-- Notices -->
          <div class="flex items-center gap-2">
            <div class="relative">
              <img src="/src/assets/help/Notifications.png" class="w-6 h-6" alt="Notifications" />
              <div class="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-cyan-400 rounded-full"></div>
            </div>
            <span class="text-cyan-400 text-sm font-medium">Notices (2)</span>
          </div>
        </div>

        <!-- Right Section: Selected Store -->
        <div class="flex items-center gap-2 text-white relative" data-dropdown="store">
          <span class="text-sm">Selected Store:</span>
          <button
            @click="toggleStoreDropdown"
            class="flex items-center gap-2 font-medium hover:text-gray-200 transition-colors"
          >
            <span>{{ selectedStore }}</span>
            <svg class="w-3 h-3 transform transition-transform duration-200" :class="{ 'rotate-180': showStoreDropdown }" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>

          <!-- Store Dropdown Menu -->
          <div
            v-if="showStoreDropdown"
            class="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
          >
            <button
              @click="selectStore('All Stores')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg"
            >
              Default
            </button>
            <button
              @click="selectStore('Unused Store 2X')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 last:rounded-b-lg"
            >
              Unused Store 2X
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Original header (when not in Help mode) -->
    <div v-else class="py-3 px-6 w-full border-t-0 bg-slate-800 border-slate-300">
      <div class="flex items-center justify-between w-full">
        <!-- Left Section: Employee Dashboard -->
        <div class="flex items-center gap-8">
          <h1 class="text-lg font-bold text-white whitespace-nowrap">
            Employee Dashboard
          </h1>
          <nav class="flex flex-auto gap-2.5 items-center font-medium text-cyan-500">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/d400eb976c922c99bad2cd919136d3aceda18ed7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Customize icon"
            />
            <button class="self-stretch my-auto hover:text-cyan-400 transition-colors">
              Customize
            </button>
            <div
              class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
              role="separator"
            ></div>
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/7a05d92f70cf51d839867d2fe6d49070fd3373dd?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Help icon"
            />
            <button
              @click="navigateToHelp"
              class="self-stretch my-auto hover:text-cyan-400 transition-colors"
              :class="{ 'text-cyan-300': showHelp }"
            >
              Help
            </button>
            <div
              class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
              role="separator"
            ></div>
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/c58da17850dd94a2cc9da6f7712ca837ad8d3b9a?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Notices icon"
            />
            <button class="self-stretch my-auto hover:text-cyan-400 transition-colors">
              Notices (2)
            </button>
          </nav>
        </div>

        <!-- Right Section: Selected Store -->
        <div class="flex items-center gap-2 text-white relative" data-dropdown="store">
          <span class="text-sm">Selected Store:</span>
          <button
            @click="toggleStoreDropdown"
            class="flex items-center gap-2 font-medium hover:text-gray-200 transition-colors"
          >
            <span>{{ selectedStore }}</span>
            <svg class="w-3 h-3 transform transition-transform duration-200" :class="{ 'rotate-180': showStoreDropdown }" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>

          <!-- Store Dropdown Menu -->
          <div
            v-if="showStoreDropdown"
            class="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
          >
            <button
              @click="selectStore('All Stores')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg"
            >
              Default
            </button>
            <button
              @click="selectStore('Unused Store 2X')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 last:rounded-b-lg"
            >
              Unused Store 2X
            </button>
          </div>
        </div>
      </div>

      <nav v-if="!showHelp" class="mt-4 w-full text-sm overflow-visible">
        <div class="flex items-center justify-between min-w-max">
          <div class="flex items-center gap-6">
            <!-- Category Dropdown -->
            <div class="relative" data-dropdown="category">
              <div class="flex items-center gap-2.5">
                <div class="text-white text-sm font-normal font-['Roboto'] leading-tight">Category:</div>
                <button
                  @click="toggleCategoryDropdown"
                  class="flex items-center gap-2 text-white text-sm font-medium font-['Roboto'] leading-tight hover:text-cyan-300 transition-colors"
                >
                  <span>{{ employeeStore.selectedCategory?.name || 'Select Category' }}</span>
                  <img
                    src="@/assets/rectangle_stroke.svg"
                    alt="dropdown arrow"
                    class="w-3.5 h-2 transform transition-transform duration-200"
                    :class="{ 'rotate-180': showCategoryDropdown }"
                  />
                </button>
              </div>

              <!-- Category Dropdown Menu -->
              <div
                v-if="showCategoryDropdown"
                class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-[100] min-w-[180px]"
                style="position: absolute; top: 100%; left: 0;"
              >
                <button
                  v-for="category in employeeStore.categories"
                  :key="category.id"
                  @click="selectCategory(category.id)"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
                  :class="{ 'bg-blue-50 text-blue-700': category.id === employeeStore.selectedCategoryId }"
                >
                  {{ category.name }}
                </button>
              </div>
            </div>

            <!-- Queue Dropdown -->
            <div class="relative" data-dropdown="queue">
              <div class="flex items-center gap-2.5">
                <div class="text-white text-sm font-normal font-['Roboto'] leading-tight">Queue:</div>
                <button
                  @click="toggleQueueDropdown"
                  class="flex items-center gap-2 text-white text-sm font-medium font-['Roboto'] leading-tight hover:text-cyan-300 transition-colors"
                  :disabled="!employeeStore.selectedCategory"
                >
                  <span>{{ employeeStore.selectedQueue?.name || 'Select Queue' }}</span>
                  <img
                    src="@/assets/rectangle_stroke.svg"
                    alt="dropdown arrow"
                    class="w-3.5 h-2 transform transition-transform duration-200"
                    :class="{ 'rotate-180': showQueueDropdown }"
                  />
                </button>
              </div>

              <!-- Queue Dropdown Menu -->
              <div
                v-if="showQueueDropdown && employeeStore.availableQueues.length > 0"
                class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-[100] min-w-[160px]"
                style="position: absolute; top: 100%; left: 0;"
              >
                <button
                  v-for="queue in employeeStore.availableQueues"
                  :key="queue.id"
                  @click="selectQueue(queue.id)"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
                  :class="{ 'bg-blue-50 text-blue-700': queue.id === employeeStore.selectedQueueId }"
                >
                  {{ queue.name }}
                </button>
              </div>
            </div>
          </div>

          <!-- Collapse Widgets -->
          <div class="inline-flex justify-start items-center gap-2">
            <button
              @click="widgetsStore.toggleCollapse()"
              class="flex items-center gap-2 hover:bg-slate-700 p-1 rounded transition-colors duration-200"
            >
              <div class="w-6 h-6 relative rounded overflow-hidden bg-slate-600 flex items-center justify-center">
                <img
                  src="@/assets/icon_collapse.svg"
                  alt="collapse icon"
                  class="w-3 h-3 transform transition-transform duration-300"
                  :class="{ 'rotate-180': widgetsStore.isCollapsed }"
                />
              </div>
              <div class="text-right justify-start text-white text-sm font-medium font-roboto leading-tight">
                {{ widgetsStore.isCollapsed ? 'Expand Widgets' : 'Collapse Widgets' }}
              </div>
            </button>
          </div>
        </div>
      </nav>
    </div>

    <!-- Content Area -->
    <div v-if="!showHelp" class="bg-slate-100 p-4">
      <div class="max-w-[1304px] mx-auto">
        <div
          :class="[
            'grid gap-5 mb-4 transition-all duration-300',
            widgetsStore.isCollapsed ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1 lg:grid-cols-3'
          ]"
        >
          <section class="w-full">
            <StatsWidget
              title="Loans by Denied Reason"
              :value="employeeStore.currentData.totalValue"
              :items="employeeStore.currentData.statsItems"
            />
          </section>

          <section class="w-full">
            <DeniedReasonsChart />
          </section>

          <section class="w-full">
            <TopReasonsWidget />
          </section>
        </div>

        <DeniedQueueTable />
      </div>
    </div>

    <!-- Help Content -->
    <div v-else>
      <HelpContent />
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWidgetsStore } from '@/stores/widgets'
import { useEmployeeStore } from '@/stores/employee'
import StatsWidget from './StatsWidget.vue'
import DeniedReasonsChart from './DeniedReasonsChart.vue'
import TopReasonsWidget from './TopReasonsWidget.vue'
import DeniedQueueTable from './DeniedQueueTable.vue'
import HelpContent from './HelpContent.vue'

// Router
const router = useRouter()

// Use stores
const widgetsStore = useWidgetsStore()
const employeeStore = useEmployeeStore()

// Store dropdown state
const selectedStore = ref('Unused Store 2X')
const showStoreDropdown = ref(false)

// Dropdown states
const showCategoryDropdown = ref(false)
const showQueueDropdown = ref(false)

// Store dropdown methods
const toggleStoreDropdown = () => {
  showStoreDropdown.value = !showStoreDropdown.value
}

const selectStore = (store: string) => {
  showStoreDropdown.value = false

  if (store === 'All Stores') {
    selectedStore.value = 'All Stores'
    router.push('/dashboard')
  } else if (store === 'Unused Store 2X') {
    selectedStore.value = 'Unused Store 2X'
    // Stay on current page
  }
}



// Dropdown toggle methods
const toggleCategoryDropdown = () => {
  showCategoryDropdown.value = !showCategoryDropdown.value
  showQueueDropdown.value = false // Close other dropdown
  showStoreDropdown.value = false // Close store dropdown
}

const toggleQueueDropdown = () => {
  showQueueDropdown.value = !showQueueDropdown.value
  showCategoryDropdown.value = false // Close other dropdown
  showStoreDropdown.value = false // Close store dropdown
}

// Category and Queue selection methods
const selectCategory = (categoryId: string) => {
  employeeStore.setCategory(categoryId)
  showCategoryDropdown.value = false
  // Generate new random data when category changes
  employeeStore.generateRandomData()
}

const selectQueue = (queueId: string) => {
  employeeStore.setQueue(queueId)
  showQueueDropdown.value = false
  // Generate new random data when queue changes
  employeeStore.generateRandomData()
}

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement

  // Check if click is outside category dropdown
  const categoryDropdown = target.closest('[data-dropdown="category"]')
  if (!categoryDropdown) {
    showCategoryDropdown.value = false
  }

  // Check if click is outside queue dropdown
  const queueDropdown = target.closest('[data-dropdown="queue"]')
  if (!queueDropdown) {
    showQueueDropdown.value = false
  }

  // Check if click is outside store dropdown
  const storeDropdown = target.closest('[data-dropdown="store"]')
  if (!storeDropdown) {
    showStoreDropdown.value = false
  }


}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Help state
const showHelp = ref(false)

// Navigate to Help page
const navigateToHelp = () => {
  showHelp.value = !showHelp.value
}
</script>
