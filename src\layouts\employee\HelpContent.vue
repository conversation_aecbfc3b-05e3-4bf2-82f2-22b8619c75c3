<template>
  <div class="w-full bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <div class="w-full bg-sky-100 py-8">
      <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-4">
          <h1 class="text-teal-950 text-4xl font-bold font-roboto leading-normal">
            Need Help?
          </h1>
        </div>
        <div class="text-center mb-8">
          <p class="text-teal-950 text-2xl font-light font-roboto leading-normal">
            Make a search or contact us, we will be happy to help you.
          </p>
        </div>

        <!-- Search Input -->
        <div class="relative w-[511px] mx-auto">
          <div class="w-full h-10 bg-slate-50 rounded-xl border border-slate-300 relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search for help..."
              class="w-full h-full px-4 bg-transparent text-sm font-normal font-roboto text-slate-700 placeholder-slate-500 border-none outline-none rounded-xl"
              @input="handleSearch"
            />
            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div class="w-4 h-4 bg-slate-300 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-6 py-8">

      <!-- Search Results Section -->
      <section v-if="isSearching && searchQuery.trim()" class="mb-16">
        <h2 class="text-teal-950 text-2xl font-bold font-roboto leading-normal mb-6">
          Search Results for "{{ searchQuery }}"
        </h2>

        <div v-if="searchResults.length === 0" class="text-center py-8">
          <p class="text-gray-500 text-lg">No results found. Try different keywords.</p>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="result in searchResults"
            :key="result.id"
            class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start justify-between mb-3">
              <h3 class="text-teal-950 text-lg font-bold font-roboto">
                {{ result.title }}
              </h3>
              <span class="px-3 py-1 bg-cyan-100 text-cyan-700 text-xs font-medium rounded-full">
                {{ result.type }}
              </span>
            </div>
            <p class="text-gray-700 text-base font-roboto mb-2">
              {{ result.content }}
            </p>
            <span class="text-cyan-600 text-sm font-medium">
              Category: {{ result.category }}
            </span>
          </div>
        </div>
      </section>

      <!-- Useful Links Section -->
      <section v-if="!isSearching || !searchQuery.trim()" class="mb-16">
        <h2 class="text-teal-950 text-4xl font-bold font-roboto leading-normal mb-4">
          Useful Links
        </h2>
        <p class="text-teal-950 text-2xl font-light font-roboto leading-normal mb-12">
          We included guides that might be useful for our users.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
          <button
            v-for="link in usefulLinks"
            :key="link.title"
            @click="openLink(link.url)"
            class="flex items-center gap-3 p-2 text-left hover:bg-gray-50 rounded-lg transition-colors"
          >
            <div class="w-5 h-2.5 bg-cyan-600 flex-shrink-0"></div>
            <span class="text-cyan-600 text-sm font-bold font-roboto leading-10">
              {{ link.title }}
            </span>
          </button>
        </div>
      </section>

      <!-- Frequently Asked Questions Section -->
      <section v-if="!isSearching || !searchQuery.trim()" class="mb-16">
        <h2 class="text-teal-950 text-4xl font-bold font-roboto leading-normal mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-teal-950 text-2xl font-light font-roboto leading-normal mb-12">
          Training website for EPIC Loans Systems Software
        </p>
        
        <div class="max-w-5xl mx-auto">
          <div class="w-full border-b border-slate-300 mb-8"></div>
          <div class="space-y-6">
            <div v-for="faq in faqs" :key="faq.question" class="mb-6">
              <h3 class="text-teal-950 text-xl font-bold font-roboto leading-loose mb-2">
                {{ faq.question }}
              </h3>
              <div class="text-teal-950 text-base font-normal font-roboto leading-loose mb-4" v-html="faq.answer"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- Latest Bulletins Section -->
      <section v-if="!isSearching || !searchQuery.trim()">
        <h2 class="text-teal-950 text-4xl font-bold font-roboto leading-normal mb-4">
          Latest Bulletins
        </h2>
        <p class="text-teal-950 text-2xl font-light font-roboto leading-normal mb-12">
          Make sure to check our bulletins frequently so you don't miss any news or updates
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          <div
            v-for="bulletin in bulletins"
            :key="bulletin.id"
            class="bg-white rounded-2xl border border-slate-300 overflow-hidden hover:shadow-lg transition-shadow"
          >
            <!-- Header with icon and tag -->
            <div class="bg-sky-100 p-4 relative">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-2">
                  <div class="w-1.5 h-1.5 bg-cyan-600 rounded-full"></div>
                  <span class="text-cyan-600 text-sm font-medium font-roboto">{{ bulletin.tag }}</span>
                </div>
              </div>
              <div class="flex justify-center">
                <div class="w-14 h-14 bg-cyan-600 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Content -->
            <div class="p-4">
              <h3 class="text-teal-950 text-lg font-bold font-roboto mb-2">
                {{ bulletin.title }}
              </h3>
              <p class="text-teal-950 text-sm font-normal font-roboto mb-3 line-clamp-2">
                {{ bulletin.description }}
              </p>
              <p class="text-slate-500 text-sm font-normal font-roboto">
                {{ bulletin.date }}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface SearchItem {
  id: number
  title: string
  content: string
  type: string
  category: string
}

// Search functionality
const searchQuery = ref('')
const searchResults = ref<SearchItem[]>([])
const isSearching = ref(false)

// Mock search data
const searchData = ref([
  {
    id: 1,
    title: 'How to reach your EPIC Agent for questions?',
    content: 'You can call any one of us at ************ or you can email <NAME_EMAIL>',
    type: 'FAQ',
    category: 'Contact'
  },
  {
    id: 2,
    title: 'Beginners guide',
    content: 'Complete guide for new users to get started with EPIC Loans Systems',
    type: 'Guide',
    category: 'Getting Started'
  },
  {
    id: 3,
    title: 'Security and privacy',
    content: 'Learn about our security measures and privacy policies',
    type: 'Guide',
    category: 'Security'
  },
  {
    id: 4,
    title: 'Can you get funds the same day as you fill in your application?',
    content: 'We do offer SAME DAY ACH, which for $XX, you will get your requested funds the same business day',
    type: 'FAQ',
    category: 'Funding'
  },
  {
    id: 5,
    title: 'Advanced tips',
    content: 'Advanced features and tips for experienced users',
    type: 'Guide',
    category: 'Advanced'
  },
  {
    id: 6,
    title: 'What are my responsibilities once I fill in my application?',
    content: 'Once you receive your Loan Agreement, you need to read it and sign it electronically',
    type: 'FAQ',
    category: 'Application'
  },
  {
    id: 7,
    title: 'Understanding your clients',
    content: 'Guide to better understand and serve your clients effectively',
    type: 'Guide',
    category: 'Client Management'
  },
  {
    id: 8,
    title: 'Important update released',
    content: 'Version 3.0.2 is out now. It consists of bug fixes and improved features',
    type: 'Bulletin',
    category: 'Updates'
  },
  // Useful Links
  {
    id: 9,
    title: 'Beginners guide',
    content: 'Complete guide for new users to get started with EPIC Loans Systems',
    type: 'Useful Link',
    category: 'Getting Started'
  },
  {
    id: 10,
    title: 'More about EPIC',
    content: 'Learn more about EPIC Loans Systems and our services',
    type: 'Useful Link',
    category: 'About'
  },
  {
    id: 11,
    title: 'The ultimate guide',
    content: 'Comprehensive guide covering all aspects of the system',
    type: 'Useful Link',
    category: 'Documentation'
  },
  {
    id: 12,
    title: 'Getting started',
    content: 'Quick start guide to help you begin using the system',
    type: 'Useful Link',
    category: 'Getting Started'
  },
  {
    id: 13,
    title: 'Testimonials and success stories',
    content: 'Read success stories and testimonials from our users',
    type: 'Useful Link',
    category: 'Success Stories'
  },
  {
    id: 14,
    title: 'Security and privacy',
    content: 'Information about our security measures and privacy policies',
    type: 'Useful Link',
    category: 'Security'
  },
  {
    id: 15,
    title: "What's next?",
    content: 'Discover what features and updates are coming next',
    type: 'Useful Link',
    category: 'Roadmap'
  },
  {
    id: 16,
    title: 'Advanced tips',
    content: 'Advanced tips and tricks for power users',
    type: 'Useful Link',
    category: 'Advanced'
  },
  {
    id: 17,
    title: 'Understanding your clients',
    content: 'Guide to better understand and serve your clients effectively',
    type: 'Useful Link',
    category: 'Client Management'
  }
])

const handleSearch = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    isSearching.value = false
    return
  }

  isSearching.value = true
  const query = searchQuery.value.toLowerCase()

  searchResults.value = searchData.value.filter(item =>
    item.title.toLowerCase().includes(query) ||
    item.content.toLowerCase().includes(query) ||
    item.category.toLowerCase().includes(query)
  )
}

// Useful Links data
const usefulLinks = ref([
  { title: 'Beginners guide', url: '#beginners' },
  { title: 'More about EPIC', url: '#about-epic' },
  { title: 'The ultimate guide', url: '#ultimate' },
  { title: 'Getting started', url: '#getting-started' },
  { title: 'Testimonials and success stories', url: '#testimonials' },
  { title: 'Security and privacy', url: '#security' },
  { title: "What's next?", url: '#whats-next' },
  { title: 'Advanced tips', url: '#advanced' },
  { title: 'Understanding your clients', url: '#clients' }
])

// FAQ data
const faqs = ref([
  {
    question: 'How to reach your EPIC Agent for questions?',
    answer: 'You can call any one of us at ************ or you can email us at <span class="text-cyan-600 font-bold"><EMAIL></span>'
  },
  {
    question: 'Can you get funds the same day as you fill in your application?',
    answer: 'We do offer SAME DAY ACH, which for $XX, you will get your requested funds the same business day as long as your Loan Agreement is signed by 12:00 PM Eastern.'
  },
  {
    question: 'How longs does it usually take to get your funds deposited in your account?',
    answer: 'It usually take one to two business days for you new funds to be deposited into your bank account electronically.'
  },
  {
    question: 'What are my responsibilities once I fill in my application?',
    answer: 'Once you receive your Loan Agreement, you need to read it and sign it electronically, which means type-in your name in the different fields highlighted in green, and once done, you click the "I AGREE" button at the very bottom. Note that it is OK to copy and paste your name into the different signature fields. If anyone field differs, from another, it will not accept it.'
  },
  {
    question: 'How can I get to my Loan Agreement for signing or to download it once I have signed it?',
    answer: 'We emailed you a link to your Portal, but if you have misplaced that email, please call us at ************, and remember that your Email address is your User name and your password are the LAST FOUR digits of your SSN (Social Security number).'
  },
  {
    question: 'Could I have funds ADDED to my existing loan?',
    answer: 'Absolutely, please call us at ************, and we can arrange to have additional funds added to your loan.'
  },
  {
    question: 'What do I do if I know that I will not have the money to make my next payment?',
    answer: 'It is imperative that you call us at least 2 days PRIOR to your loan due date, so we can PUSH the date a few days back. Note that if we delete a payment, the amount would balloon your very last payment. You do have the ability to make additional payments any time you wish, this will of course reduce your number of payments, and shorten the life of your loan.'
  },
  {
    question: 'I missed my payment, what do I do now?',
    answer: 'Don\'t worry, call us at ************, and we will make payment arrangements for you. Please note that it is imperative that you reach out to us to set up an affordable payment arrangement as soon as possible as to avoid going to Collections.'
  }
])

// Latest Bulletins data
const bulletins = ref([
  {
    id: 1,
    tag: 'Urgent',
    title: 'Important update released',
    description: 'Version 3.0.2 is out now. It consists of bug fixes and improved...',
    date: 'AUG 10, 2023'
  },
  {
    id: 2,
    tag: 'New',
    title: 'New guide available',
    description: 'Our new user guide "Advanced Tips", is now available in our si...',
    date: 'JUL 28, 2023'
  },
  {
    id: 3,
    tag: 'Update',
    title: 'Latest update is here',
    description: 'Version 3.0.1 is out now. It consists of design improvements...',
    date: 'JUL 25, 2023'
  }
])

// Methods
const openLink = (url: string) => {
  console.log('Opening link:', url)
  // TODO: Implement link navigation
}
</script>
