<template>
  <header class="flex flex-wrap gap-3 lg:gap-5 justify-between py-2.5 px-3 lg:pr-6 lg:pl-1.5 w-full bg-white shadow-[0px_2px_12px_rgba(0,0,0,0.05)] border-b border-gray-200">
    <div class="flex gap-3.5 self-start">
      <div class="flex gap-1.5">
        <button
          @click="toggleSidebar"
          class="hover:opacity-80 transition-opacity duration-200"
          aria-label="Toggle sidebar"
        >
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/8aa352046bde861dd9cb0b98904b26054bbb8e7f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-10 rounded-lg aspect-square"
            alt="Company logo"
          />
        </button>
        <div class="shrink-0 my-auto w-px h-4 border border-solid border-slate-300"></div>
      </div>
      <img
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/8d2019e145d5ce1ae222efb945bb787f5d6f0856?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
        class="object-contain shrink-0 self-start aspect-[2.16] w-[84px]"
        alt="Brand logo"
      />
    </div>
    <nav class="flex gap-2 lg:gap-3.5 items-center text-base leading-none text-sky-950">
      <div class="flex flex-wrap gap-2 lg:gap-3.5 items-center self-stretch">
        <!-- Customer Search Component -->
        <div class="hidden sm:block">
          <CustomerSearch />
        </div>
        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300 hidden sm:block"></div>
        <div class="flex gap-1.5 items-center self-stretch my-auto min-h-6">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/e05d46c8d2cdbbb79175dca4efe75c5543179e3d?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
            alt="Leads icon"
          />
          <span class="self-stretch my-auto">
            <span style="font-weight: 700">28</span> New Leads
          </span>
        </div>
        <div class="flex gap-1.5 items-center self-stretch my-auto min-h-6">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/35adfdf0ac4b21794088c003868092b796346bfa?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
            alt="Messages icon"
          />
          <span class="self-stretch my-auto">
            <span style="font-weight: 700">3</span> Messages
          </span>
        </div>
        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"></div>
      </div>
      <!-- User Profile Dropdown -->
      <div class="relative">
        <button
          @click="toggleProfileDropdown"
          class="flex gap-1.5 items-center font-bold hover:bg-gray-50 px-2 py-1 rounded transition-colors duration-200"
          :aria-expanded="showProfileDropdown"
          aria-haspopup="true"
        >
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/f19481c55b74a302c9e9c657d8b6c58d2bafae71?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-6 aspect-square"
            alt="User avatar"
          />
          <span class="my-auto">{{ userName }}</span>
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-3.5 aspect-[1.56] transform transition-transform duration-200"
            :class="{ 'rotate-180': showProfileDropdown }"
            alt="Dropdown arrow"
          />
        </button>

        <!-- Profile Dropdown Menu -->
        <div
          v-if="showProfileDropdown"
          class="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
        >
          <!-- Admin View Option -->
          <button
            @click="navigateToAdmin"
            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg flex items-center gap-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Admin View
          </button>

          <!-- Logout Option -->
          <button
            @click="handleLogout"
            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 last:rounded-b-lg flex items-center gap-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Logout
          </button>
        </div>
      </div>
    </nav>

    <!-- Permission Denied Modal -->
    <PermissionDeniedModal
      :is-visible="showPermissionModal"
      @close="showPermissionModal = false"
    />
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import PermissionDeniedModal from '@/components/PermissionDeniedModal.vue'
import CustomerSearch from '@/components/CustomerSearch.vue'

// Define emits
interface Emits {
  (e: 'toggle-sidebar'): void
}

const emit = defineEmits<Emits>()
const router = useRouter()
const userStore = useUserStore()

// Profile dropdown state
const showProfileDropdown = ref(false)
const showPermissionModal = ref(false)

// Computed properties
const userName = computed(() => userStore.user?.name ?? 'John Doe') // Default name for demo

// Toggle sidebar function
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

// Profile dropdown functions
const toggleProfileDropdown = () => {
  showProfileDropdown.value = !showProfileDropdown.value
}

const navigateToAdmin = () => {
  showProfileDropdown.value = false
  // Show permission denied modal
  showPermissionModal.value = true
}

const handleLogout = () => {
  showProfileDropdown.value = false
  // Handle logout logic
  console.log('Logging out...')
  userStore.logout()
  // Redirect to dashboard
  router.push('/dashboard')
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const dropdown = target.closest('.relative')
  if (!dropdown) {
    showProfileDropdown.value = false
  }
}

// Add/remove event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
